<%
local api = require "luci.xqnetwork.api"
-%>

<script type="text/javascript">
	//<![CDATA[
	function getXHR() {
		if (typeof XHR === 'object' && typeof XHR.create === 'function') {
			return XHR.create();
		} else if (typeof XHR === 'function') {
			return new XHR();
		} else {
			throw new Error("XHR is not supported in this environment.");
		}
	}

	function ajax_add_node(link) {
		var chunkSize = 1000;  // 分片发送以突破uhttpd的限制，每块1000字符
		var totalChunks = Math.ceil(link.length / chunkSize);
		var currentChunk = 0;

		function sendNextChunk() {
			if (currentChunk < totalChunks) {
				var chunk = link.substring(currentChunk * chunkSize, (currentChunk + 1) * chunkSize);
				var xhr = getXHR();
				xhr.post('<%=api.url("link_add_node")%>', {
					'chunk': chunk,
					'chunk_index': currentChunk,
					'total_chunks': totalChunks
				}, function(x, data) {
					if (x && x.status === 200) {
						currentChunk++;
						sendNextChunk();
					} else {
						alert("<%:Error%>");
					}
				});
			} else {
				window.location.href = '<%=api.url("node_list")%>';
			}
		}
		sendNextChunk();
	}

	function open_add_link_div() {
		document.getElementById("add_link_div").style.display = "block";
		document.getElementById("nodes_link").focus();
	}

	function close_add_link_div() {
		document.getElementById("add_link_div").style.display = "none";
	}

	function add_node() {
		var nodes_link = document.getElementById("nodes_link").value;
		nodes_link = nodes_link.replace(/\t/g, "").replace(/\r\n|\r/g, "\n").trim();
		if (nodes_link != "") {
			var s = nodes_link.split('://');
			if (s.length > 1) {
				ajax_add_node(nodes_link);
			}
			else {
				alert("<%:Please enter the correct link.%>");
			}
		}
		else {
			document.getElementById("nodes_link").focus();
		}
	}

	function clear_all_nodes() {
		if (confirm('<%:Are you sure to clear all nodes?%>') == true){
			XHR.get('<%=api.url("clear_all_nodes")%>', null,
			function(x, data) {
				if(x && x.status == 200) {
					window.location.href = '<%=api.url("node_list")%>';
				}
				else {
					alert("<%:Error%>");
				}
			});
		}
	}

	//]]>
</script>

<div id="add_link_div">
	<div id="add_link_modal_container">
		<h3><%:Add the node via the link%></h3>
		<div class="cbi-value">
			<textarea id="nodes_link" rows="10"></textarea>
			<p id="nodes_link_text"><%:Enter share links, one per line. Subscription links are not supported!%></p>
		</div>
		<div id="add_link_button_container">
			<input class="btn cbi-button cbi-button-add" type="button" onclick="add_node()" value="<%:Add%>" />
			<input class="btn cbi-button cbi-button-remove" type="button" onclick="close_add_link_div()" value="<%:Close%>" />
		</div>
	</div>
</div>

<div class="cbi-value">
	<label class="cbi-value-title"></label>
	<div class="cbi-value-field">
		<input class="btn cbi-button cbi-button-add" type="submit" name="cbi.cts.xqnetwork.nodes." value="<%:Add%>" />
		<input class="btn cbi-button cbi-button-add" type="button" onclick="open_add_link_div()" value="<%:Add the node via the link%>" />
		<input class="btn cbi-button cbi-button-remove" type="button" onclick="clear_all_nodes()" value="<%:Clear all nodes%>" />
		<input class="btn cbi-button cbi-button-remove" type="button" onclick="delete_select_nodes()" value="<%:Delete select nodes%>" />
		<input class="btn cbi-button" type="button" onclick="checked_all_node(this)" value="<%:Select all%>" />
		<input class="btn cbi-button cbi-button-apply" type="submit" name="cbi.apply" value="<%:Save & Apply%>" />
		<input class="btn cbi-button cbi-button-save" type="submit" name="cbi.save" value="<%:Save%>" />
		<input class="btn cbi-button cbi-button-reset" type="button" value="<%:Reset%>" onclick="location.href='<%=REQUEST_URI%>'" />
		<div id="div_node_count"></div>
	</div>
</div>

<style>
	#add_link_div {
		display: none;
		position: fixed;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background: white;
		padding: 20px;
		border: 2px solid #ccc;
		box-shadow: 0 0 10px rgba(0,0,0,0.5);
		z-index: 1000;
		width: 90%;
		max-width: 500px;
	}

	#add_link_modal_container {
		width: 100%;
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-bottom: 20px;
	}

	#nodes_link {
		width: 100%;
		height: 180px;
		resize: vertical;
		font-family: monospace;
		padding: 5px;
		border: 1px solid #ccc;
		border-radius: 5px;
	}

	#nodes_link_text {
		color: red;
		font-size: 14px;
		margin-top: 5px;
		text-align: center;
		width: 100%;
	}

	#add_link_button_container {
		display: flex;
		justify-content: space-between;
		width: 100%;
		max-width: 300px;
		margin-top: 10px;
	}
</style>
