# OpenWrt Passwall 服务器编译脚本

本项目提供了两个shell脚本，用于在服务器上直接编译OpenWrt Passwall的所有组件。

## 脚本说明

### 1. `build_passwall.sh` - 完整版编译脚本

功能强大的编译脚本，支持多平台编译，包含完整的错误处理和日志记录。

**特性:**
- 支持多个硬件平台 (x86_64, aarch64, arm, mips等)
- 完整的依赖检查和自动安装
- 详细的编译日志和错误报告
- 自动打包编译结果
- 支持批量编译所有平台

**支持的平台:**
- x86_64 (默认)
- aarch64_generic
- aarch64_cortex-a53
- arm_cortex-a7_neon-vfpv4
- mips_24kc
- mipsel_24kc
- 等等...

### 2. `quick_build.sh` - 快速编译脚本

简化版脚本，专门用于快速编译x86_64平台，适合日常开发和测试。

**特性:**
- 专注于x86_64平台
- 简化的配置流程
- 快速编译和打包
- 适合初学者使用

## 使用方法

### 准备工作

1. **系统要求:**
   - Ubuntu 20.04+ 或 Debian 11+
   - 至少 20GB 可用磁盘空间
   - 4GB+ 内存推荐

2. **给脚本添加执行权限:**
   ```bash
   chmod +x build_passwall.sh quick_build.sh
   ```

### 使用完整版脚本

1. **查看帮助信息:**
   ```bash
   ./build_passwall.sh --help
   ```

2. **列出支持的平台:**
   ```bash
   ./build_passwall.sh --list
   ```

3. **编译默认平台 (x86_64):**
   ```bash
   ./build_passwall.sh
   ```

4. **编译指定平台:**
   ```bash
   ./build_passwall.sh --platform mips_24kc
   ```

5. **编译所有平台:**
   ```bash
   ./build_passwall.sh --all
   ```

6. **清理工作目录:**
   ```bash
   ./build_passwall.sh --clean
   ```

### 使用快速编译脚本

1. **开始编译:**
   ```bash
   ./quick_build.sh build
   ```

2. **清理环境:**
   ```bash
   ./quick_build.sh clean
   ```

3. **查看帮助:**
   ```bash
   ./quick_build.sh help
   ```

## 编译产物

编译完成后，IPK文件将保存在以下位置：

- **完整版脚本:** `build_workspace/output/`
- **快速脚本:** `build_quick/output/`

编译产物包括：
- `luci-app-xqnetwork_*.ipk` - 主应用
- 各种代理工具的IPK包 (shadowsocks, xray, sing-box等)
- 压缩包形式的完整包集合

## 编译的组件

脚本会编译以下组件：

### 核心组件
- **luci-app-xqnetwork** - PassWall主应用
- **chinadns-ng** - 中国DNS解析
- **dns2socks** - DNS转SOCKS代理

### 代理工具
- **shadowsocks-rust** - Shadowsocks Rust版本
- **shadowsocksr-libev** - ShadowsocksR
- **xray-core** - Xray核心
- **sing-box** - 新一代代理工具
- **hysteria** - 基于QUIC的代理
- **naiveproxy** - 基于Chromium的代理
- **trojan-plus** - Trojan增强版

### 辅助工具
- **v2ray-geodata** - 地理位置数据
- **geoview** - 地理位置查看器
- **tcping** - TCP连接测试
- **simple-obfs** - 简单混淆插件

## 故障排除

### 常见问题

1. **依赖安装失败:**
   ```bash
   sudo apt-get update
   sudo apt-get upgrade
   # 然后重新运行脚本
   ```

2. **下载速度慢:**
   - 可以考虑使用代理
   - 或者手动下载SDK后放到指定目录

3. **编译内存不足:**
   - 减少并行编译数量
   - 增加swap空间

4. **某些包编译失败:**
   - 这是正常现象，脚本会继续编译其他包
   - 检查日志了解具体错误原因

### 日志查看

编译过程中的详细日志会显示在终端，包括：
- 绿色: 信息日志
- 黄色: 警告信息  
- 红色: 错误信息
- 蓝色: 步骤标识

### 清理和重新开始

如果遇到问题需要重新开始：

```bash
# 完整版脚本
./build_passwall.sh --clean

# 快速脚本
./quick_build.sh clean

# 手动清理
rm -rf build_workspace build_quick
```

## 自定义配置

### 修改编译选项

可以编辑脚本中的配置部分来自定义编译选项：

1. **修改包含的组件:**
   编辑脚本中的 `CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_*` 选项

2. **添加新的平台:**
   在 `SDK_CONFIGS` 数组中添加新的平台和对应的SDK URL

3. **修改源码仓库:**
   修改 `PACKAGES_REPO` 和 `XQNETWORK_REPO` 变量

## 注意事项

1. **编译时间:** 完整编译可能需要30分钟到2小时，取决于服务器性能
2. **网络要求:** 需要稳定的网络连接下载源码和依赖
3. **磁盘空间:** 每个平台大约需要5-10GB空间
4. **权限要求:** 脚本需要sudo权限安装系统依赖

## 技术支持

如果遇到问题，可以：
1. 查看脚本输出的详细日志
2. 检查网络连接和磁盘空间
3. 参考OpenWrt官方文档
4. 在相关GitHub仓库提交issue

## 许可证

本脚本基于原项目的许可证，遵循GPL v3协议。
