#!/bin/bash

#
# OpenWrt Passwall 自动编译脚本
# 适用于在服务器上直接编译所有组件
# 作者: 基于 SMALLPROGRAM 的 GitHub Action 配置改编
#

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 配置变量
XQNETWORK_REPO="hosemorinho/openwrt-xqnetwork"
PACKAGES_REPO="xiaorouji/openwrt-passwall-packages"
PACKAGE_NAMES="chinadns-ng dns2socks geoview hysteria ipt2socks microsocks naiveproxy tcping trojan-plus tuic-client shadowsocks-rust shadowsocksr-libev simple-obfs sing-box v2ray-geodata v2ray-plugin xray-core xray-plugin shadow-tls"

# SDK配置 - 支持多个平台
declare -A SDK_CONFIGS
SDK_CONFIGS[x86_64]="https://downloads.openwrt.org/releases/24.10.1/targets/x86/64/openwrt-sdk-24.10.1-x86-64_gcc-13.3.0_musl.Linux-x86_64.tar.zst"
SDK_CONFIGS[aarch64_generic]="https://downloads.openwrt.org/releases/24.10.1/targets/rockchip/armv8/openwrt-sdk-24.10.1-rockchip-armv8_gcc-13.3.0_musl.Linux-x86_64.tar.zst"
SDK_CONFIGS[aarch64_cortex-a53]="https://downloads.openwrt.org/releases/24.10.1/targets/mvebu/cortexa53/openwrt-sdk-24.10.1-mvebu-cortexa53_gcc-13.3.0_musl.Linux-x86_64.tar.zst"
SDK_CONFIGS[arm_cortex-a7_neon-vfpv4]="https://downloads.openwrt.org/releases/24.10.1/targets/sunxi/cortexa7/openwrt-sdk-24.10.1-sunxi-cortexa7_gcc-13.3.0_musl_eabi.Linux-x86_64.tar.zst"
SDK_CONFIGS[mips_24kc]="https://downloads.openwrt.org/releases/24.10.1/targets/ath79/generic/openwrt-sdk-24.10.1-ath79-generic_gcc-13.3.0_musl.Linux-x86_64.tar.zst"
SDK_CONFIGS[mipsel_24kc]="https://downloads.openwrt.org/releases/24.10.1/targets/ramips/rt288x/openwrt-sdk-24.10.1-ramips-rt288x_gcc-13.3.0_musl.Linux-x86_64.tar.zst"

# 默认平台
DEFAULT_PLATFORM="x86_64"

# 工作目录
WORK_DIR="$(pwd)/build_workspace"
SDK_VER="24.10"

# 显示帮助信息
show_help() {
    echo "OpenWrt Passwall 自动编译脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -p, --platform PLATFORM    指定编译平台 (默认: x86_64)"
    echo "  -a, --all                   编译所有支持的平台"
    echo "  -l, --list                  列出所有支持的平台"
    echo "  -c, --clean                 清理工作目录"
    echo "  -h, --help                  显示此帮助信息"
    echo ""
    echo "支持的平台:"
    for platform in "${!SDK_CONFIGS[@]}"; do
        echo "  - $platform"
    done
    echo ""
    echo "示例:"
    echo "  $0                          # 编译默认平台 (x86_64)"
    echo "  $0 -p mips_24kc            # 编译 mips_24kc 平台"
    echo "  $0 -a                      # 编译所有平台"
    echo "  $0 -c                      # 清理工作目录"
}

# 列出支持的平台
list_platforms() {
    echo "支持的编译平台:"
    for platform in "${!SDK_CONFIGS[@]}"; do
        echo "  - $platform"
    done
}

# 清理工作目录
clean_workspace() {
    log_step "清理工作目录..."
    if [ -d "$WORK_DIR" ]; then
        rm -rf "$WORK_DIR"
        log_info "工作目录已清理"
    else
        log_info "工作目录不存在，无需清理"
    fi
}

# 检查系统依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    local missing_deps=()
    local required_deps=(
        "build-essential" "clang" "flex" "bison" "g++" "gawk" 
        "gcc-multilib" "g++-multilib" "gettext" "git" "libncurses-dev" 
        "libssl-dev" "python3-distutils" "python3-setuptools" "rsync" 
        "swig" "unzip" "zlib1g-dev" "file" "wget" "zstd"
    )
    
    for dep in "${required_deps[@]}"; do
        if ! dpkg -l | grep -q "^ii  $dep "; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_warn "发现缺失的依赖包，正在安装..."
        sudo apt-get update -qq
        sudo apt-get install -qq -y "${missing_deps[@]}"
        log_info "依赖包安装完成"
    else
        log_info "所有依赖包已安装"
    fi
}

# 下载并解压SDK
download_sdk() {
    local platform=$1
    local sdk_url=${SDK_CONFIGS[$platform]}
    
    if [ -z "$sdk_url" ]; then
        log_error "不支持的平台: $platform"
        return 1
    fi
    
    log_step "下载 $platform SDK..."
    
    local sdk_dir="$WORK_DIR/sdk_$platform"
    mkdir -p "$sdk_dir"
    
    local file_name=$(basename "$sdk_url")
    local download_path="$WORK_DIR/$file_name"
    
    # 检查是否已下载
    if [ ! -f "$download_path" ]; then
        wget -O "$download_path" "$sdk_url"
        log_info "SDK 下载完成"
    else
        log_info "SDK 文件已存在，跳过下载"
    fi
    
    # 解压SDK
    log_step "解压 SDK..."
    if [ ! -d "$sdk_dir/scripts" ]; then
        tar --zstd -x -f "$download_path" -C "$sdk_dir" --strip-components=1
        log_info "SDK 解压完成"
    else
        log_info "SDK 已解压，跳过解压步骤"
    fi
    
    echo "$sdk_dir"
}

# 配置feeds
configure_feeds() {
    local sdk_dir=$1
    local platform=$2
    
    log_step "配置 feeds for $platform..."
    
    cd "$sdk_dir"
    
    # 创建feeds配置
    cat > feeds.conf << EOF
src-git base https://github.com/openwrt/openwrt.git;openwrt-$SDK_VER
src-git packages https://github.com/openwrt/packages.git;openwrt-$SDK_VER
src-git luci https://github.com/openwrt/luci.git;openwrt-$SDK_VER
src-git routing https://git.openwrt.org/feed/routing.git;openwrt-$SDK_VER
src-git xqnetwork_packages https://github.com/$PACKAGES_REPO.git;main
src-git xqnetwork https://github.com/$XQNETWORK_REPO.git;main
EOF
    
    log_info "Feeds 配置文件已创建"
    cat feeds.conf
    
    # 更新feeds
    log_step "更新 feeds..."
    ./scripts/feeds update -a || (sleep 5 && ./scripts/feeds update -a)
    
    # 安装packages
    log_step "安装 packages..."
    ./scripts/feeds install -a -f -p xqnetwork_packages
    ./scripts/feeds install -d y luci-app-xqnetwork
    
    # 更新golang (如果需要)
    if [ -d "feeds/packages/lang/golang" ]; then
        rm -rf feeds/packages/lang/golang
        git clone https://github.com/sbwml/packages_lang_golang -b 24.x feeds/packages/lang/golang
        log_info "Golang 包已更新"
    fi
    
    log_info "Feeds 配置完成"
}

# 配置编译选项
configure_build() {
    local sdk_dir=$1
    local platform=$2

    log_step "配置编译选项 for $platform..."

    cd "$sdk_dir"

    # 创建.config文件
    cat > .config << EOF
CONFIG_ALL_NONSHARED=n
CONFIG_ALL_KMODS=n
CONFIG_ALL=n
CONFIG_AUTOREMOVE=n
CONFIG_SIGNED_PACKAGES=n
CONFIG_LUCI_LANG_zh_Hans=y
CONFIG_PACKAGE_luci-app-xqnetwork=m
CONFIG_PACKAGE_luci-app-xqnetwork_Iptables_Transparent_Proxy=y
CONFIG_PACKAGE_luci-app-xqnetwork_Nftables_Transparent_Proxy=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Geoview=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Haproxy=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Hysteria=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_NaiveProxy=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Libev_Client=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Libev_Server=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Rust_Client=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Rust_Server=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_ShadowsocksR_Libev_Client=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_ShadowsocksR_Libev_Server=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadow_TLS=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Simple_Obfs=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_SingBox=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Trojan_Plus=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_tuic_client=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_V2ray_Geodata=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_V2ray_Plugin=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Xray=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Xray_Plugin=y
EOF

    # 生成默认配置
    make defconfig

    log_info "编译配置完成"
}

# 下载源码
download_sources() {
    local sdk_dir=$1
    local platform=$2

    log_step "下载源码 for $platform..."

    cd "$sdk_dir"

    # 下载所有依赖的源码
    make download -j8

    # 检查下载失败的文件
    find dl -size -1024c -exec ls -l {} \; 2>/dev/null || true

    log_info "源码下载完成"
}

# 编译packages
compile_packages() {
    local sdk_dir=$1
    local platform=$2

    log_step "开始编译 packages for $platform..."

    cd "$sdk_dir"

    local failed_packages=()
    local success_count=0
    local total_count=0

    for package in $PACKAGE_NAMES; do
        total_count=$((total_count + 1))
        if [ -d "feeds/xqnetwork_packages/$package" ]; then
            log_info "编译 $package..."
            if make package/feeds/xqnetwork_packages/$package/compile -j$(nproc) V=s; then
                log_info "✓ $package 编译成功"
                success_count=$((success_count + 1))
            else
                log_error "✗ $package 编译失败"
                failed_packages+=("$package")
            fi
        else
            log_warn "跳过 $package (目录不存在)"
        fi
    done

    # 编译主应用
    log_step "编译 luci-app-xqnetwork..."
    if make package/luci-app-xqnetwork/{clean,compile} -j$(nproc) V=s; then
        log_info "✓ luci-app-xqnetwork 编译成功"
        success_count=$((success_count + 1))
    else
        log_error "✗ luci-app-xqnetwork 编译失败"
        failed_packages+=("luci-app-xqnetwork")
    fi

    # 输出编译结果
    echo ""
    log_info "编译完成统计:"
    log_info "成功: $success_count"
    log_info "失败: ${#failed_packages[@]}"

    if [ ${#failed_packages[@]} -gt 0 ]; then
        log_warn "编译失败的包:"
        for pkg in "${failed_packages[@]}"; do
            echo "  - $pkg"
        done
    fi

    return ${#failed_packages[@]}
}

# 打包结果
package_results() {
    local sdk_dir=$1
    local platform=$2

    log_step "打包编译结果 for $platform..."

    cd "$sdk_dir"

    local output_dir="$WORK_DIR/output"
    mkdir -p "$output_dir"

    # 检查是否有编译产物
    if [ -d "bin/packages" ]; then
        # 创建平台特定的输出目录
        local platform_output="$output_dir/$platform"
        mkdir -p "$platform_output"

        # 复制IPK文件
        find bin/packages -name "*.ipk" -exec cp {} "$platform_output/" \;

        # 创建压缩包
        cd "$output_dir"
        if [ -n "$(ls -A $platform_output 2>/dev/null)" ]; then
            zip -r "xqnetwork_packages_${platform}.zip" "$platform/"
            log_info "编译结果已打包: xqnetwork_packages_${platform}.zip"

            # 显示包含的文件
            log_info "包含的IPK文件:"
            ls -la "$platform_output"/*.ipk 2>/dev/null || log_warn "没有找到IPK文件"
        else
            log_warn "没有找到编译产物"
            return 1
        fi
    else
        log_error "编译目录不存在"
        return 1
    fi
}

# 编译单个平台
build_platform() {
    local platform=$1

    log_step "开始编译平台: $platform"

    # 下载SDK
    local sdk_dir
    sdk_dir=$(download_sdk "$platform")
    if [ $? -ne 0 ]; then
        log_error "SDK下载失败: $platform"
        return 1
    fi

    # 配置feeds
    configure_feeds "$sdk_dir" "$platform"
    if [ $? -ne 0 ]; then
        log_error "Feeds配置失败: $platform"
        return 1
    fi

    # 配置编译选项
    configure_build "$sdk_dir" "$platform"
    if [ $? -ne 0 ]; then
        log_error "编译配置失败: $platform"
        return 1
    fi

    # 下载源码
    download_sources "$sdk_dir" "$platform"
    if [ $? -ne 0 ]; then
        log_error "源码下载失败: $platform"
        return 1
    fi

    # 编译packages
    compile_packages "$sdk_dir" "$platform"
    local compile_result=$?

    # 打包结果
    package_results "$sdk_dir" "$platform"
    if [ $? -ne 0 ]; then
        log_error "结果打包失败: $platform"
        return 1
    fi

    if [ $compile_result -eq 0 ]; then
        log_info "平台 $platform 编译完全成功!"
    else
        log_warn "平台 $platform 编译部分成功 (有 $compile_result 个包编译失败)"
    fi

    return $compile_result
}

# 主函数
main() {
    local platforms=()
    local clean_only=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--platform)
                platforms+=("$2")
                shift 2
                ;;
            -a|--all)
                platforms=($(printf '%s\n' "${!SDK_CONFIGS[@]}" | sort))
                shift
                ;;
            -l|--list)
                list_platforms
                exit 0
                ;;
            -c|--clean)
                clean_only=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 如果只是清理，执行清理后退出
    if [ "$clean_only" = true ]; then
        clean_workspace
        exit 0
    fi

    # 如果没有指定平台，使用默认平台
    if [ ${#platforms[@]} -eq 0 ]; then
        platforms=("$DEFAULT_PLATFORM")
    fi

    # 验证平台
    for platform in "${platforms[@]}"; do
        if [ -z "${SDK_CONFIGS[$platform]}" ]; then
            log_error "不支持的平台: $platform"
            list_platforms
            exit 1
        fi
    done

    log_info "OpenWrt Passwall 自动编译脚本启动"
    log_info "编译平台: ${platforms[*]}"

    # 检查依赖
    check_dependencies

    # 创建工作目录
    mkdir -p "$WORK_DIR"

    # 编译每个平台
    local failed_platforms=()
    local success_platforms=()

    for platform in "${platforms[@]}"; do
        echo ""
        log_step "======== 编译平台: $platform ========"

        if build_platform "$platform"; then
            success_platforms+=("$platform")
        else
            failed_platforms+=("$platform")
        fi
    done

    # 输出最终结果
    echo ""
    echo "========================================"
    log_info "编译完成!"
    echo "========================================"

    if [ ${#success_platforms[@]} -gt 0 ]; then
        log_info "成功编译的平台:"
        for platform in "${success_platforms[@]}"; do
            echo "  ✓ $platform"
        done
    fi

    if [ ${#failed_platforms[@]} -gt 0 ]; then
        log_warn "编译失败的平台:"
        for platform in "${failed_platforms[@]}"; do
            echo "  ✗ $platform"
        done
    fi

    log_info "编译结果保存在: $WORK_DIR/output/"

    # 如果有失败的平台，返回非零退出码
    if [ ${#failed_platforms[@]} -gt 0 ]; then
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
