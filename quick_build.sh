#!/bin/bash

#
# OpenWrt Passwall 快速编译脚本
# 简化版本，适用于快速编译x86_64平台
#

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 配置
SDK_URL="https://downloads.openwrt.org/releases/24.10.1/targets/x86/64/openwrt-sdk-24.10.1-x86-64_gcc-13.3.0_musl.Linux-x86_64.tar.zst"
PACKAGES_REPO="xiaorouji/openwrt-passwall-packages"
XQNETWORK_REPO="hosemorinho/openwrt-xqnetwork"
WORK_DIR="./build_quick"
SDK_VER="24.10"

# 检查并安装依赖
install_deps() {
    log_step "检查并安装系统依赖..."
    
    sudo apt-get update -qq
    sudo apt-get install -y \
        build-essential clang flex bison g++ gawk \
        gcc-multilib g++-multilib gettext git \
        libncurses-dev libssl-dev python3-distutils \
        python3-setuptools rsync swig unzip \
        zlib1g-dev file wget zstd zip
    
    log_info "依赖安装完成"
}

# 下载并设置SDK
setup_sdk() {
    log_step "设置 OpenWrt SDK..."
    
    mkdir -p "$WORK_DIR"
    cd "$WORK_DIR"
    
    # 下载SDK
    if [ ! -f "sdk.tar.zst" ]; then
        log_info "下载 SDK..."
        wget -O sdk.tar.zst "$SDK_URL"
    fi
    
    # 解压SDK
    if [ ! -d "sdk" ]; then
        log_info "解压 SDK..."
        mkdir sdk
        tar --zstd -x -f sdk.tar.zst -C ./sdk --strip-components=1
    fi
    
    cd sdk
    log_info "SDK 设置完成"
}

# 配置feeds
setup_feeds() {
    log_step "配置 feeds..."
    
    # 创建feeds配置
    cat > feeds.conf << EOF
src-git base https://github.com/openwrt/openwrt.git;openwrt-$SDK_VER
src-git packages https://github.com/openwrt/packages.git;openwrt-$SDK_VER
src-git luci https://github.com/openwrt/luci.git;openwrt-$SDK_VER
src-git routing https://git.openwrt.org/feed/routing.git;openwrt-$SDK_VER
src-git xqnetwork_packages https://github.com/$PACKAGES_REPO.git;main
src-git xqnetwork https://github.com/$XQNETWORK_REPO.git;main
EOF
    
    # 更新feeds
    log_info "更新 feeds..."
    ./scripts/feeds update -a || (sleep 5 && ./scripts/feeds update -a)
    
    # 安装packages
    log_info "安装 packages..."
    ./scripts/feeds install -a -f -p xqnetwork_packages
    ./scripts/feeds install -d y luci-app-xqnetwork
    
    # 更新golang
    if [ -d "feeds/packages/lang/golang" ]; then
        rm -rf feeds/packages/lang/golang
        git clone https://github.com/sbwml/packages_lang_golang -b 24.x feeds/packages/lang/golang
    fi
    
    log_info "Feeds 配置完成"
}

# 配置编译选项
setup_config() {
    log_step "配置编译选项..."
    
    cat > .config << EOF
CONFIG_ALL_NONSHARED=n
CONFIG_ALL_KMODS=n
CONFIG_ALL=n
CONFIG_AUTOREMOVE=n
CONFIG_SIGNED_PACKAGES=n
CONFIG_LUCI_LANG_zh_Hans=y
CONFIG_PACKAGE_luci-app-xqnetwork=m
CONFIG_PACKAGE_luci-app-xqnetwork_Iptables_Transparent_Proxy=y
CONFIG_PACKAGE_luci-app-xqnetwork_Nftables_Transparent_Proxy=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Geoview=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Haproxy=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Hysteria=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_NaiveProxy=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Libev_Client=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Libev_Server=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Rust_Client=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Rust_Server=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_ShadowsocksR_Libev_Client=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_ShadowsocksR_Libev_Server=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadow_TLS=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Simple_Obfs=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_SingBox=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Trojan_Plus=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_tuic_client=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_V2ray_Geodata=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_V2ray_Plugin=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Xray=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Xray_Plugin=y
EOF
    
    make defconfig
    log_info "编译配置完成"
}

# 下载源码
download_sources() {
    log_step "下载源码..."
    make download -j8
    log_info "源码下载完成"
}

# 编译
compile_all() {
    log_step "开始编译..."
    
    # 编译主应用
    log_info "编译 luci-app-xqnetwork..."
    make package/luci-app-xqnetwork/{clean,compile} -j$(nproc) V=s
    
    # 编译所有相关包
    local packages="chinadns-ng dns2socks geoview hysteria ipt2socks microsocks naiveproxy tcping trojan-plus tuic-client shadowsocks-rust shadowsocksr-libev simple-obfs sing-box v2ray-geodata v2ray-plugin xray-core xray-plugin shadow-tls"
    
    for package in $packages; do
        if [ -d "feeds/xqnetwork_packages/$package" ]; then
            log_info "编译 $package..."
            make package/feeds/xqnetwork_packages/$package/compile -j$(nproc) V=s || log_warn "$package 编译失败，继续..."
        fi
    done
    
    log_info "编译完成"
}

# 打包结果
package_results() {
    log_step "打包编译结果..."
    
    mkdir -p ../output
    
    if [ -d "bin/packages" ]; then
        # 复制所有IPK文件
        find bin/packages -name "*.ipk" -exec cp {} ../output/ \;
        
        # 创建压缩包
        cd ../output
        zip -r "passwall_packages_x86_64_$(date +%Y%m%d_%H%M%S).zip" *.ipk
        
        log_info "编译结果已打包到 output 目录"
        log_info "包含的文件:"
        ls -la *.ipk 2>/dev/null || log_warn "没有找到IPK文件"
    else
        log_error "没有找到编译产物"
        return 1
    fi
}

# 清理函数
clean_build() {
    log_step "清理编译环境..."
    if [ -d "$WORK_DIR" ]; then
        rm -rf "$WORK_DIR"
        log_info "清理完成"
    fi
}

# 显示帮助
show_help() {
    echo "OpenWrt Passwall 快速编译脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build    - 执行完整编译流程"
    echo "  clean    - 清理编译环境"
    echo "  help     - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build    # 开始编译"
    echo "  $0 clean    # 清理环境"
}

# 主函数
main() {
    case "${1:-build}" in
        build)
            log_info "开始 OpenWrt Passwall 快速编译..."
            install_deps
            setup_sdk
            setup_feeds
            setup_config
            download_sources
            compile_all
            package_results
            log_info "编译完成! 结果保存在 $WORK_DIR/output/"
            ;;
        clean)
            clean_build
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
