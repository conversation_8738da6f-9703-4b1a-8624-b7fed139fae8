#!/bin/sh

uci -q batch <<-EOF >/dev/null
	set dhcp.@dnsmasq[0].localuse=1
	commit dhcp
	[ -e "/etc/config/ucitrack" ] && {
	delete ucitrack.@xqnetwork[-1]
	add ucitrack xqnetwork
	set ucitrack.@xqnetwork[-1].init=xqnetwork
	commit ucitrack
	}
	delete firewall.xqnetwork
	set firewall.xqnetwork=include
	set firewall.xqnetwork.type=script
	set firewall.xqnetwork.path=/var/etc/xqnetwork.include
	set firewall.xqnetwork.reload=1
	commit firewall
	[ -e "/etc/config/ucitrack" ] && {
	delete ucitrack.@xqnetwork_server[-1]
	add ucitrack xqnetwork_server
	set ucitrack.@xqnetwork_server[-1].init=xqnetwork_server
	commit ucitrack
	}
	delete firewall.xqnetwork_server
	set firewall.xqnetwork_server=include
	set firewall.xqnetwork_server.type=script
	set firewall.xqnetwork_server.path=/var/etc/xqnetwork_server.include
	set firewall.xqnetwork_server.reload=1
	commit firewall
	set uhttpd.main.max_requests=50
	commit uhttpd
EOF

[ ! -s "/etc/config/xqnetwork" ] && cp -f /usr/share/xqnetwork/0_default_config /etc/config/xqnetwork

chmod +x /usr/share/xqnetwork/*.sh

## 4.77-5 below upgrade to 4.77-6 above
[ -e "/etc/config/xqnetwork_show" ] && rm -rf /etc/config/xqnetwork_show

[ "$(uci -q get xqnetwork.@global_xray[0].sniffing)" == "1" ] && [ "$(uci -q get xqnetwork.@global_xray[0].route_only)" != "1" ] && uci -q set xqnetwork.@global_xray[0].sniffing_override_dest=1
uci -q delete xqnetwork.@global_xray[0].sniffing
uci -q delete xqnetwork.@global_xray[0].route_only
uci -q commit xqnetwork

rm -f /tmp/luci-indexcache
rm -rf /tmp/luci-modulecache/
killall -HUP rpcd 2>/dev/null

exit 0
