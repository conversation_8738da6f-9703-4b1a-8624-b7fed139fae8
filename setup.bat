@echo off
echo OpenWrt Passwall 编译脚本设置
echo ================================

echo.
echo 正在设置脚本权限...

REM 在Windows下，shell脚本需要在Linux环境中运行
echo.
echo 注意: 这些shell脚本需要在Linux环境中运行
echo.
echo 使用方法:
echo 1. 将这些文件上传到您的Linux服务器
echo 2. 在Linux服务器上运行以下命令:
echo.
echo    chmod +x build_passwall.sh quick_build.sh
echo    ./quick_build.sh build
echo.
echo 或者使用完整版脚本:
echo    ./build_passwall.sh --help
echo.

echo 文件列表:
echo - build_passwall.sh    (完整版编译脚本)
echo - quick_build.sh       (快速编译脚本)  
echo - BUILD_README.md      (详细使用说明)
echo.

echo 设置完成!
pause
