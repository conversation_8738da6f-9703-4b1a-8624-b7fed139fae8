
#
# Copyright (c) 2022-2025 SMALLPROGRAM <https://github.com/smallprogram>
# Description: Auto compile for luci-app-xqnetwork
#
name: "Auto compile with openwrt sdk"
on:
  repository_dispatch:
  workflow_dispatch:
    inputs:
      ssh:
        description: 'SSH connection to Actions'
        required: false
        default: 'false'
env:
  TZ: Asia/Shanghai
  xqnetwork: hosemorinho/openwrt-xqnetwork
  packages: xiaorouji/openwrt-passwall-packages
  package_names: "chinadns-ng dns2socks geoview hysteria ipt2socks microsocks naiveproxy tcping trojan-plus tuic-client shadowsocks-rust shadowsocksr-libev simple-obfs sing-box v2ray-geodata v2ray-plugin xray-core xray-plugin shadow-tls"

jobs:
  job_check:
    name: Check Version
    runs-on: ubuntu-22.04
    outputs:
      xqnetwork_version: ${{ steps.check_version.outputs.latest_version }}
      has_update: ${{ steps.check_version.outputs.has_update }}
      prerelease: ${{ steps.check_version.outputs.prerelease }}
    steps:
      - name: Checkout
        uses: actions/checkout@main
        with:
          fetch-depth: 0
          ref: ${{ github.ref_name }}

      - name: Check version
        id: check_version
        env:
          url_tags: https://api.github.com/repos/${{ env.xqnetwork }}/tags
        run: |
          cd luci-app-xqnetwork
          latest_version=$(awk -F ':=' '/PKG_VERSION|PKG_RELEASE/ {print $2}' Makefile | sed ':a;N;s/\$(PKG_VERSION)-//;s/\n$//;s/\n/-/;ba')
          has_update=$([ -z "$(wget -qO- -t1 -T2 ${{env.url_tags}} | grep \"${latest_version}\")" ] && echo true || echo false)
          prerelease=$([ "${{ github.ref_name }}" == "main" ] && echo false || echo true)
          echo "latest_version=${latest_version}" >> $GITHUB_OUTPUT
          echo "has_update=${has_update}" >> $GITHUB_OUTPUT
          echo "prerelease=${prerelease}" >> $GITHUB_OUTPUT
          echo "latest_version: ${latest_version}"
          echo "has_update: ${has_update}"
          echo "prerelease: ${prerelease}"

      - name: Prepare release
        if: steps.check_version.outputs.has_update == 'true'
        run: |
          echo "## :mega:Update content" >> release.txt
          echo "![](https://img.shields.io/github/downloads/${{ env.xqnetwork }}/${{steps.check_version.outputs.latest_version}}/total?style=flat-square)" >> release.txt
          echo "### XQNetwork Info" >> release.txt
          echo "**:minidisc: XQNetwork Version: ${{steps.check_version.outputs.latest_version}}**" >> release.txt
          touch release.txt

      - name: Generate new tag & release
        if: steps.check_version.outputs.has_update == 'true'
        uses: softprops/action-gh-release@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{steps.check_version.outputs.latest_version}}
          target_commitish: ${{ github.ref_name }}
          prerelease: ${{steps.check_version.outputs.prerelease}}
          body_path: release.txt

  job_build_xqnetwork:
    name: Build xqnetwork [Luci ${{ matrix.luci_ver }}]
    needs: job_check
    if: needs.job_check.outputs.has_update == 'true'
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        include:
          - sdk_ver: "21.02"
            luci_ver: "19.07"
            sdk_url: https://downloads.openwrt.org/releases/21.02.7/targets/x86/64/openwrt-sdk-21.02.7-x86-64_gcc-8.4.0_musl.Linux-x86_64.tar.xz
          - sdk_ver: "24.10"
            luci_ver: "24.10"
            sdk_url: https://downloads.openwrt.org/releases/24.10.1/targets/x86/64/openwrt-sdk-24.10.1-x86-64_gcc-13.3.0_musl.Linux-x86_64.tar.zst
    steps:
      - name: Install packages
        run: |
          sudo -E rm -rf /usr/share/dotnet /etc/mysql /etc/php /etc/apt/sources.list.d /usr/local/lib/android
          echo "Install packages"
          sudo -E apt-get -qq update
          sudo -E apt-get -qq install build-essential clang flex bison g++ gawk gcc-multilib g++-multilib gettext git libncurses-dev libssl-dev python3-distutils python3-setuptools rsync swig unzip zlib1g-dev file wget
          sudo -E apt-get -qq autoremove --purge
          sudo -E apt-get -qq clean

      - name: Cache openwrt SDK
        id: cache-sdk
        uses: actions/cache@v4
        with:
          path: sdk
          key: openwrt-luci-${{ matrix.luci_ver }}-x86_64

      - name: Initialization environment
        if: steps.cache-sdk.outputs.cache-hit != 'true'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN_XQNETWORK }}
        run: |
          wget ${{ matrix.sdk_url }}
          file_name=$(echo ${{ matrix.sdk_url }} | awk -F/ '{print $NF}')
          mkdir sdk && tar --zstd -x -f $file_name -C ./sdk --strip-components=1
          cd sdk
          echo "src-git base https://github.com/openwrt/openwrt.git;openwrt-${{ matrix.sdk_ver }}" > feeds.conf
          echo "src-git packages https://github.com/openwrt/packages.git;openwrt-${{ matrix.sdk_ver }}" >> feeds.conf
          echo "src-git luci https://github.com/openwrt/luci.git;openwrt-${{ matrix.luci_ver }}" >> feeds.conf
          echo "src-git routing https://git.openwrt.org/feed/routing.git;openwrt-${{ matrix.sdk_ver }}" >> feeds.conf
          echo "src-git xqnetwork_packages https://github.com/${{ env.packages }}.git;main" >> feeds.conf
          echo "src-git xqnetwork https://github.com/${{ env.xqnetwork }}.git;main" >> feeds.conf
          cat feeds.conf
          rm -rf feeds/xqnetwork
          git clone --depth 1 -b main https://${GITHUB_TOKEN_XQNETWORK}@github.com/${{ env.xqnetwork }}.git feeds/xqnetwork || echo "xqnetwork clone failed"
          ls -la feeds/xqnetwork || echo "xqnetwork feed not found"
          ./scripts/feeds update -a || (sleep 5 && ./scripts/feeds update -a)
          if [ -d "feeds/xqnetwork/luci-app-xqnetwork" ]; then
            echo "luci-app-xqnetwork found"
            ls -la feeds/xqnetwork/luci-app-xqnetwork
          else
            echo "luci-app-xqnetwork not found"
          fi
          rm -rf feeds/packages/lang/golang
          git clone https://github.com/sbwml/packages_lang_golang -b 24.x feeds/packages/lang/golang
          ./scripts/feeds install -d y luci-app-xqnetwork
          make package/luci-app-xqnetwork/download -j8

      - name: Update xqnetwork feeds
        if: steps.cache-sdk.outputs.cache-hit == 'true'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN_XQNETWORK }}
        run: |
          cd sdk
          sed -i 's/main/${{ github.ref_name }}/' feeds.conf
          rm -rf feeds/xqnetwork
          git clone --depth 1 -b main https://${GITHUB_TOKEN_XQNETWORK}@github.com/${{ env.xqnetwork }}.git feeds/xqnetwork || echo "xqnetwork clone failed"
          ./scripts/feeds update xqnetwork_packages xqnetwork
          ls -la feeds/xqnetwork || echo "xqnetwork feed not found"
          ./scripts/feeds install -d y luci-app-xqnetwork

      - name: Compile xqnetwork
        id: compile
        run: |
          cd sdk
          echo "CONFIG_ALL_NONSHARED=n" > .config
          echo "CONFIG_ALL_KMODS=n" >> .config
          echo "CONFIG_ALL=n" >> .config
          echo "CONFIG_AUTOREMOVE=n" >> .config
          echo "CONFIG_LUCI_LANG_zh_Hans=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork=m" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_Iptables_Transparent_Proxy=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_Nftables_Transparent_Proxy=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Geoview=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Haproxy=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Hysteria=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_NaiveProxy=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Libev_Client=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Libev_Server=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Rust_Client=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Rust_Server=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_ShadowsocksR_Libev_Client=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_ShadowsocksR_Libev_Server=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadow_TLS=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Simple_Obfs=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_SingBox=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Trojan_Plus=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_tuic_client=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_V2ray_Geodata=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_V2ray_Plugin=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Xray=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Xray_Plugin=y" >> .config
          make defconfig
          echo "make package/luci-app-xqnetwork/{clean,compile} -j$(nproc)"
          make package/luci-app-xqnetwork/{clean,compile} -j$(nproc) V=s
          mv bin/packages/x86_64/xqnetwork/ ../
          make clean
          rm .config .config.old
          rm -rf feeds/xqnetwork feeds/xqnetwork.*
          cd ../xqnetwork
          for i in $(ls); do mv $i luci-${{ matrix.luci_ver }}_$i; done
          cd ..
          echo "status=success" >> $GITHUB_OUTPUT
          echo "FIRMWARE=$PWD" >> $GITHUB_ENV

      - name: Upload xqnetwork ipks to release
        uses: softprops/action-gh-release@v2
        if: steps.compile.outputs.status == 'success'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{needs.job_check.outputs.xqnetwork_version}}
          files: ${{ env.FIRMWARE }}/xqnetwork/*.ipk

  job_auto_compile:
    if: ${{ needs.job_check.outputs.has_update == 'true' && needs.job_check.outputs.prerelease == 'false' }}
    needs: job_check
    runs-on: ubuntu-22.04
    name: build (${{ matrix.platform }})
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: x86_64
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/x86/64/openwrt-sdk-24.10.1-x86-64_gcc-13.3.0_musl.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: aarch64_generic
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/rockchip/armv8/openwrt-sdk-24.10.1-rockchip-armv8_gcc-13.3.0_musl.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: aarch64_cortex-a53
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/mvebu/cortexa53/openwrt-sdk-24.10.1-mvebu-cortexa53_gcc-13.3.0_musl.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: aarch64_cortex-a72
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/mvebu/cortexa72/openwrt-sdk-24.10.1-mvebu-cortexa72_gcc-13.3.0_musl.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: arm_cortex-a5_vfpv4
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/at91/sama5/openwrt-sdk-24.10.1-at91-sama5_gcc-13.3.0_musl_eabi.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: arm_cortex-a7
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/mediatek/mt7629/openwrt-sdk-24.10.1-mediatek-mt7629_gcc-13.3.0_musl_eabi.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: arm_cortex-a7_neon-vfpv4
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/sunxi/cortexa7/openwrt-sdk-24.10.1-sunxi-cortexa7_gcc-13.3.0_musl_eabi.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: arm_cortex-a8_vfpv3
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/sunxi/cortexa8/openwrt-sdk-24.10.1-sunxi-cortexa8_gcc-13.3.0_musl_eabi.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: arm_cortex-a9
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/bcm53xx/generic/openwrt-sdk-24.10.1-bcm53xx-generic_gcc-13.3.0_musl_eabi.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: arm_cortex-a9_neon
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/zynq/generic/openwrt-sdk-24.10.1-zynq-generic_gcc-13.3.0_musl_eabi.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: arm_cortex-a9_vfpv3-d16
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/mvebu/cortexa9/openwrt-sdk-24.10.1-mvebu-cortexa9_gcc-13.3.0_musl_eabi.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: arm_cortex-a15_neon-vfpv4
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/ipq806x/generic/openwrt-sdk-24.10.1-ipq806x-generic_gcc-13.3.0_musl_eabi.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: mips_24kc
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/ath79/generic/openwrt-sdk-24.10.1-ath79-generic_gcc-13.3.0_musl.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: mips_4kec
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/realtek/rtl838x/openwrt-sdk-24.10.1-realtek-rtl838x_gcc-13.3.0_musl.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: mips_mips32
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/bcm53xx/generic/openwrt-sdk-24.10.1-bcm53xx-generic_gcc-13.3.0_musl_eabi.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: mipsel_24kc
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/ramips/rt288x/openwrt-sdk-24.10.1-ramips-rt288x_gcc-13.3.0_musl.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: mipsel_74kc
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/ramips/rt3883/openwrt-sdk-24.10.1-ramips-rt3883_gcc-13.3.0_musl.Linux-x86_64.tar.zst
            sdk_ver: "24.10"
          - platform: mipsel_mips32
            url_sdk: https://downloads.openwrt.org/releases/24.10.1/targets/bcm47xx/generic/openwrt-sdk-24.10.1-bcm47xx-generic_gcc-13.3.0_musl.Linux-x86_64.tar.zst
            sdk_ver: "24.10"

    steps:
      - name: Initialization ${{ matrix.platform }} compile environment
        run: |
          sudo -E rm -rf /usr/share/dotnet /etc/mysql /etc/php /etc/apt/sources.list.d /usr/local/lib/android
          echo "install packages!!!!!!"
          sudo -E apt-get -qq update
          sudo -E apt-get -qq install build-essential clang flex bison g++ gawk gcc-multilib g++-multilib gettext git libncurses-dev libssl-dev python3-distutils python3-setuptools rsync swig unzip zlib1g-dev file wget
          sudo -E apt-get -qq autoremove --purge
          sudo -E apt-get -qq clean

      - name: ${{ matrix.platform }} sdk download
        run: |
          wget ${{ matrix.url_sdk }}
          file_name=$(echo ${{ matrix.url_sdk }} | awk -F/ '{print $NF}')
          mkdir sdk && tar --zstd -x -f $file_name -C ./sdk --strip-components=1
          cd sdk

      - name: SSH connection to Actions
        uses: mxschmitt/action-tmate@v3.13
        if: (github.event.inputs.ssh == 'true' && github.event.inputs.ssh != 'false') || contains(github.event.action, 'ssh')

      - name: ${{ matrix.platform }} feeds configuration packages
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN_XQNETWORK }}
        run: |
          cd sdk
          echo "src-git base https://github.com/openwrt/openwrt.git;openwrt-${{ matrix.sdk_ver }}" > feeds.conf
          echo "src-git packages https://github.com/openwrt/packages.git;openwrt-${{ matrix.sdk_ver }}" >> feeds.conf
          echo "src-git luci https://github.com/openwrt/luci.git;openwrt-${{ matrix.sdk_ver }}" >> feeds.conf
          echo "src-git routing https://git.openwrt.org/feed/routing.git;openwrt-${{ matrix.sdk_ver }}" >> feeds.conf
          echo "src-git xqnetwork_packages https://github.com/${{ env.packages }}.git;main" >> feeds.conf
          echo "src-git xqnetwork https://github.com/${{ env.xqnetwork }}.git;main" >> feeds.conf
          cat feeds.conf
          rm -rf feeds/xqnetwork
          git clone --depth 1 -b main https://${GITHUB_TOKEN_XQNETWORK}@github.com/${{ env.xqnetwork }}.git feeds/xqnetwork || echo "xqnetwork clone failed"
          ls -la feeds/xqnetwork || echo "xqnetwork feed not found"
          ./scripts/feeds update -a || (sleep 5 && ./scripts/feeds update -a)
          ./scripts/feeds install -a -f -p xqnetwork_packages
          ./scripts/feeds install -d y luci-app-xqnetwork
          rm -rf feeds/packages/lang/golang
          git clone https://github.com/sbwml/packages_lang_golang -b 25.x feeds/packages/lang/golang
          echo "CONFIG_ALL_NONSHARED=n" > .config
          echo "CONFIG_ALL_KMODS=n" >> .config
          echo "CONFIG_ALL=n" >> .config
          echo "CONFIG_AUTOREMOVE=n" >> .config
          echo "CONFIG_SIGNED_PACKAGES=n" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork=m" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_Iptables_Transparent_Proxy=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_Nftables_Transparent_Proxy=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Geoview=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Haproxy=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Hysteria=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_NaiveProxy=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Libev_Client=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Libev_Server=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Rust_Client=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadowsocks_Rust_Server=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_ShadowsocksR_Libev_Client=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_ShadowsocksR_Libev_Server=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Shadow_TLS=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Simple_Obfs=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_SingBox=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Trojan_Plus=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_tuic_client=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_V2ray_Geodata=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_V2ray_Plugin=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Xray=y" >> .config
          echo "CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Xray_Plugin=y" >> .config
          make defconfig

      - name: ${{ matrix.platform }} download
        run: |
          cd sdk
          make download -j8
          find dl -size -1024c -exec ls -l {} \;

      - name: ${{ matrix.platform }} compile
        id: compile
        run: |
          cd sdk
          for package in ${{ env.package_names }}; do
            if [ -d "feeds/xqnetwork_packages/$package" ]; then
              echo "-----------begin compile $package ---------------"
              sleep 10s
              make package/feeds/xqnetwork_packages/$package/compile -j$(nproc) V=s
              echo "-----------compiled $package ---------------"
              echo ""
            fi
          done
          echo "status=success" >> $GITHUB_OUTPUT

      - name: Organize ${{ matrix.platform }} files
        id: organize
        if: steps.compile.outputs.status == 'success'
        run: |
          cd sdk
          mkdir upload
          zip -jr upload/xqnetwork_packages_ipk_${{ matrix.platform }}.zip bin/packages/*/xqnetwork_packages/*.ipk
          echo "FIRMWARE=$PWD" >> $GITHUB_ENV
          echo "status=success" >> $GITHUB_OUTPUT

      - name: Generate release info
        id: info
        if: steps.compile.outputs.status == 'success'
        run: |
          cd sdk
          echo "## :mega:Update content" >> release.txt
          echo "![](https://img.shields.io/github/downloads/${{ env.xqnetwork }}/${{needs.job_check.outputs.xqnetwork_version}}/total?style=flat-square)" >> release.txt
          echo "### XQNetwork Info" >> release.txt
          echo "**:minidisc: XQNetwork Version: ${{needs.job_check.outputs.xqnetwork_version}}**" >> release.txt
          echo "### Packages Version" >> release.txt
          echo "**package name**|**package version**" >> release.txt
          echo "-|-" >> release.txt
          pkgs=$(ls feeds/xqnetwork_packages -I v2ray-geodata | grep -E "$(echo "${{ env.package_names }}" | sed 's/ /|/g')")
          for pkg in $pkgs; do
            version=$(awk -F ':=' '/PKG_VERSION:=/{print $2}' feeds/xqnetwork_packages/$pkg/Makefile | sed 's/\r//g')
            [ -z "${version}" ] && version=$(awk -F ':=' '/PKG_SOURCE_DATE:=/{print $2}' feeds/xqnetwork_packages/$pkg/Makefile | sed 's/\r//g')
            echo "**:ice_cube: $pkg**|**${version}**" >> release.txt
          done
          echo "**:ice_cube: v2ray-geoip**|**$(awk -F ':=' '/GEOIP_VER:=/{print $2}' feeds/xqnetwork_packages/v2ray-geodata/Makefile)**" >> release.txt
          echo "**:ice_cube: v2ray-geosite**|**$(awk -F ':=' '/GEOSITE_VER:=/{print $2}' feeds/xqnetwork_packages/v2ray-geodata/Makefile)**" >> release.txt
          touch release.txt
          echo "status=success" >> $GITHUB_OUTPUT

      - name: Upload firmware to release
        uses: softprops/action-gh-release@v2
        if: steps.info.outputs.status == 'success'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{needs.job_check.outputs.xqnetwork_version}}
          body_path: ${{ env.FIRMWARE }}/release.txt
          files: ${{ env.FIRMWARE }}/upload/*