# OpenWrt Passwall 服务器编译脚本 - 使用说明

## 📁 文件说明

我为您创建了以下文件：

### 🔧 编译脚本
- **`build_passwall.sh`** - 完整版编译脚本，支持多平台
- **`quick_build.sh`** - 快速编译脚本，专注x86_64平台

### 📖 文档
- **`BUILD_README.md`** - 详细的使用文档和故障排除
- **`使用说明.md`** - 本文件，快速上手指南

### 🛠️ 辅助工具
- **`upload_to_server.sh`** - 上传脚本到服务器的工具
- **`setup.bat`** - Windows下的设置脚本

## 🚀 快速开始

### 方法一：直接在服务器上使用

1. **上传文件到服务器:**
   ```bash
   # 使用scp上传
   scp build_passwall.sh quick_build.sh BUILD_README.md user@your-server:~/
   
   # 或使用上传工具
   ./upload_to_server.sh user@your-server:~/passwall-build
   ```

2. **在服务器上设置权限:**
   ```bash
   chmod +x build_passwall.sh quick_build.sh
   ```

3. **开始编译:**
   ```bash
   # 快速编译 (推荐新手)
   ./quick_build.sh build
   
   # 或使用完整版
   ./build_passwall.sh
   ```

### 方法二：使用上传工具

```bash
# 给上传脚本添加权限
chmod +x upload_to_server.sh

# 上传到服务器
./upload_to_server.sh root@192.168.1.100:/root/build

# 然后SSH到服务器运行编译
ssh root@192.168.1.100
cd /root/build
./quick_build.sh build
```

## 📋 编译选项对比

| 特性 | quick_build.sh | build_passwall.sh |
|------|----------------|-------------------|
| 支持平台 | 仅 x86_64 | 16+ 个平台 |
| 编译时间 | 30-60分钟 | 1-3小时 |
| 适用场景 | 日常使用、测试 | 生产环境、多设备 |
| 配置复杂度 | 简单 | 完整 |
| 错误处理 | 基础 | 详细 |

## 🎯 推荐使用流程

### 新手用户
```bash
./quick_build.sh build
```

### 高级用户
```bash
# 查看支持的平台
./build_passwall.sh --list

# 编译特定平台
./build_passwall.sh --platform mips_24kc

# 编译所有平台
./build_passwall.sh --all
```

## 📦 编译产物

编译完成后，您将得到：

### IPK文件位置
- **快速脚本:** `build_quick/output/`
- **完整脚本:** `build_workspace/output/`

### 主要文件
- `luci-app-xqnetwork_*.ipk` - PassWall主程序
- `shadowsocks-rust_*.ipk` - Shadowsocks代理
- `xray-core_*.ipk` - Xray代理核心
- `sing-box_*.ipk` - Sing-box代理工具
- 以及其他相关组件...

### 压缩包
脚本会自动创建包含所有IPK的压缩包，方便下载和分发。

## ⚡ 系统要求

### 最低要求
- **系统:** Ubuntu 20.04+ / Debian 11+
- **内存:** 4GB RAM
- **存储:** 20GB 可用空间
- **网络:** 稳定的互联网连接

### 推荐配置
- **系统:** Ubuntu 22.04 LTS
- **内存:** 8GB+ RAM
- **存储:** 50GB+ SSD
- **CPU:** 4核心以上

## 🔧 常见问题

### Q: 编译失败怎么办？
A: 查看终端输出的错误信息，常见原因：
- 网络连接问题
- 磁盘空间不足
- 依赖包缺失

### Q: 某些包编译失败正常吗？
A: 是的，由于依赖关系复杂，部分包可能编译失败，但不影响其他包。

### Q: 如何加速编译？
A: 
- 使用SSD存储
- 增加内存
- 使用更多CPU核心
- 配置编译缓存

### Q: 编译的包如何安装？
A: 将IPK文件上传到OpenWrt设备，使用opkg安装：
```bash
opkg install luci-app-xqnetwork_*.ipk
```

## 🛠️ 自定义编译

### 修改包含的组件
编辑脚本中的配置选项：
```bash
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_Xray=y
CONFIG_PACKAGE_luci-app-xqnetwork_INCLUDE_SingBox=y
```

### 添加新平台
在 `build_passwall.sh` 中的 `SDK_CONFIGS` 数组添加新平台。

## 📞 技术支持

1. **查看详细文档:** `BUILD_README.md`
2. **检查编译日志:** 脚本会输出详细的编译过程
3. **清理重试:** 使用 `--clean` 选项清理后重新编译
4. **社区支持:** 在相关GitHub仓库提交issue

## 🎉 开始编译

现在您可以开始编译了！推荐从快速脚本开始：

```bash
./quick_build.sh build
```

编译完成后，在 `build_quick/output/` 目录找到您的IPK文件。

祝您编译顺利！ 🚀
