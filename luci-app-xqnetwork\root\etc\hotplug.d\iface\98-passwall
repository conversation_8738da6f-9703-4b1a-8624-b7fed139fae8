#!/bin/sh

[[ "$ACTION" == "ifup" && $(uci get "xqnetwork.@global[0].enabled") == "1" ]] && [ -f /var/lock/xqnetwork_ready.lock ] && {
	default_device=$(ip route | grep default | awk -F 'dev ' '{print $2}' | awk '{print $1}')
	[ "$default_device" == "$DEVICE" ] && {
		LOCK_FILE_DIR=/var/lock
		[ ! -d ${LOCK_FILE_DIR} ] && mkdir -p ${LOCK_FILE_DIR}
		LOCK_FILE="${LOCK_FILE_DIR}/xqnetwork_ifup.lock"
		if [ -s ${LOCK_FILE} ]; then
			SPID=$(cat ${LOCK_FILE})
			if [ -e /proc/${SPID}/status ]; then
				exit 1
			fi
			cat /dev/null > ${LOCK_FILE}
		fi
		echo $$ > ${LOCK_FILE}
		
		/etc/init.d/xqnetwork restart >/dev/null 2>&1 &
		logger -p notice -t network -s "xqnetwork: restart when $INTERFACE ifup"
		
		rm -rf ${LOCK_FILE}
	}
}
