<%
local api = require "luci.xqnetwork.api"
-%>
<script type="text/javascript">
	//<![CDATA[
	function confirmDeleteNode(remark) {
		if (!confirm("<%:Delete the subscribed node%>: " + remark + " ?"))
			return false;

		fetch('<%= api.url("subscribe_del_node") %>?remark=' + encodeURIComponent(remark), {
			method: "GET"
		}).then(res => {
			if (res.ok) {
				location.reload();
			} else {
				alert("<%:Failed to delete.%>");
			}
		});
		return false;
	}

	function confirmDeleteAll() {
		if (!confirm("<%:Are you sure you want to delete all subscribed nodes?%>"))
			return false;

		fetch('<%= api.url("subscribe_del_all") %>', {
			method: "GET"
		}).then(res => {
			if (res.ok) {
				location.reload();
			} else {
				alert("<%:Failed to delete.%>");
			}
		});
		return false;
	}

	//修正上移、下移按钮名称
	window.onload = function() {
		var ups = document.querySelectorAll("input.btn.cbi-button.cbi-button-up");
		var downs = document.querySelectorAll("input.btn.cbi-button.cbi-button-down");
		for (var i = 0; i < ups.length; i++) {
			ups[i].value = "<%:Move up%>";
		}
		for (var i = 0; i < downs.length; i++) {
			downs[i].value = "<%:Move down%>";
		}
	}
	//]]>
</script>
