<%
local api = require "luci.xqnetwork.api"
-%>
<script type="text/javascript">
	//<![CDATA[
	function clear_log(btn) {
		XHR.get('<%=api.url("server_clear_log")%>', null,
			function(x, data) {
				if(x && x.status == 200) {
					var log_textarea = document.getElementById('log_textarea');
					log_textarea.innerHTML = "";
					log_textarea.scrollTop = log_textarea.scrollHeight;
				}
			}
		);
	}

	XHR.poll(3, '<%=api.url("server_get_log")%>', null,
		function(x, data) {
			if(x && x.status == 200) {
				var log_textarea = document.getElementById('log_textarea');
				log_textarea.innerHTML = x.responseText;
				log_textarea.scrollTop = log_textarea.scrollHeight;
			}
		}
	);
	//]]>
</script>
<fieldset class="cbi-section" id="_log_fieldset">
	<legend>
		<%:Logs%>
	</legend>
	<input class="btn cbi-button cbi-button-remove" type="button" onclick="clear_log()" value="<%:Clear logs%>" />
	<textarea id="log_textarea" class="cbi-input-textarea" style="width: 100%;margin-top: 10px;" data-update="change" rows="20" wrap="off" readonly="readonly"></textarea>
</fieldset>
