#!/bin/bash

#
# 上传编译脚本到服务器的辅助脚本
# 使用方法: ./upload_to_server.sh user@server:/path/to/destination
#

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查参数
if [ $# -eq 0 ]; then
    echo "OpenWrt Passwall 编译脚本上传工具"
    echo ""
    echo "用法: $0 user@server:/path/to/destination"
    echo ""
    echo "示例:"
    echo "  $0 root@192.168.1.100:/root/passwall-build"
    echo "  $0 <EMAIL>:~/build"
    echo ""
    echo "注意: 需要已配置SSH密钥认证或准备输入密码"
    exit 1
fi

DESTINATION=$1

# 要上传的文件列表
FILES=(
    "build_passwall.sh"
    "quick_build.sh" 
    "BUILD_README.md"
)

log_info "准备上传编译脚本到服务器..."
log_info "目标: $DESTINATION"

# 检查文件是否存在
for file in "${FILES[@]}"; do
    if [ ! -f "$file" ]; then
        log_error "文件不存在: $file"
        exit 1
    fi
done

# 上传文件
log_info "开始上传文件..."

for file in "${FILES[@]}"; do
    log_info "上传 $file..."
    scp "$file" "$DESTINATION/"
done

# 设置权限的命令
SERVER_HOST=$(echo "$DESTINATION" | cut -d: -f1)
SERVER_PATH=$(echo "$DESTINATION" | cut -d: -f2)

log_info "设置脚本执行权限..."
ssh "$SERVER_HOST" "cd $SERVER_PATH && chmod +x build_passwall.sh quick_build.sh"

log_info "上传完成!"
echo ""
echo "现在您可以在服务器上运行:"
echo "  cd $SERVER_PATH"
echo "  ./quick_build.sh build        # 快速编译"
echo "  ./build_passwall.sh --help    # 查看完整版帮助"
echo ""
echo "详细使用说明请查看 BUILD_README.md 文件"
